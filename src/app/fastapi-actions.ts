// src/app/fastapi-actions.ts
// Description: This file contains actions for interacting with the FastAPI backend.
// It provides a clear separation of concerns, keeping frontend-backend communication
// isolated from the original Genkit-based TypeScript actions.

"use server";

// --- Type Definitions ---

/**
 * Defines the structure for the itinerary request, matching the Pydantic model in FastAPI.
 * Best practice: Keep frontend and backend models synchronized.
 */
interface ItineraryRequest {
    destination: string;
    duration: number;
    interests: string[];
}

/**
 * Represents a chat message in FastAPI format
 */
interface FastAPIChatMessage {
    role: "user" | "assistant";
    parts: Array<{ text: string }>;
}

/**
 * Query request structure for FastAPI chat endpoint
 */
interface QueryRequest {
    query: string;
    history: FastAPIChatMessage[];
}

/**
 * Contact inquiry request structure for FastAPI
 */
interface ContactInquiryRequest {
    name: string;
    email: string;
    message: string;
}

/**
 * Contact analysis response structure from FastAPI
 */
interface ContactAnalysisResponse {
    analysis: {
        category: string;
        sentiment: "Positive" | "Neutral" | "Negative";
        urgency: "High" | "Medium" | "Low";
        suggested_response: string;
    };
}

// --- Configuration ---

/**
 * The base URL for the FastAPI backend.
 * Using an environment variable is recommended for production.
 */
const FASTAPI_BASE_URL = process.env.FASTAPI_URL || "http://127.0.0.1:8000";

/**
 * Request timeout in milliseconds
 */
const REQUEST_TIMEOUT = 30000; // 30 seconds

/**
 * Common headers for all FastAPI requests
 */
const COMMON_HEADERS = {
    "Content-Type": "application/json",
    Accept: "application/json",
} as const;

// --- Utility Functions ---

/**
 * Creates an AbortController with timeout for request cancellation
 * @param timeoutMs - Timeout in milliseconds
 * @returns AbortController instance
 */
const createTimeoutController = (timeoutMs: number): AbortController => {
    const controller = new AbortController();
    setTimeout(() => controller.abort(), timeoutMs);
    return controller;
};

/**
 * Handles API errors with consistent error messages
 * @param error - The error object
 * @param operation - The operation that failed
 * @throws Always throws an error with formatted message
 */
const handleAPIError = (error: unknown, operation: string): never => {
    console.error(`Failed to ${operation}:`, error);

    if (error instanceof Error) {
        if (error.name === "AbortError") {
            throw new Error(
                `Request timeout: ${operation} took too long to complete`,
            );
        }
        throw new Error(`Failed to ${operation}: ${error.message}`);
    }

    throw new Error(`An unknown error occurred while ${operation}`);
};

// --- Action to Fetch Itinerary ---

/**
 * Fetches a travel itinerary from the FastAPI backend.
 * Enhanced with timeout handling and improved error messages.
 *
 * @param data - The user's preferences for the itinerary
 * @returns A promise that resolves to the itinerary string
 * @throws {Error} If the network request fails or the backend returns an error
 */
export const getItineraryFromFastAPI = async (
    data: ItineraryRequest,
): Promise<string> => {
    const { destination, duration, interests } = data;
    const endpoint = `${FASTAPI_BASE_URL}/api/suggest-itinerary`;
    const controller = createTimeoutController(REQUEST_TIMEOUT);

    console.log(`Fetching itinerary from: ${endpoint}`);

    try {
        const response = await fetch(endpoint, {
            method: "POST",
            headers: COMMON_HEADERS,
            body: JSON.stringify({
                destination,
                duration,
                interests,
            }),
            signal: controller.signal,
        });

        if (!response.ok) {
            const errorData = await response
                .json()
                .catch(() => ({ detail: "Unknown error" }));
            throw new Error(
                `Backend error: ${response.status} - ${errorData.detail}`,
            );
        }

        const result = await response.json();
        return result.itinerary;
    } catch (error) {
        return handleAPIError(error, "fetch itinerary from FastAPI");
    }
};

// --- Action to Answer Query ---

/**
 * Processes travel-related questions with AI assistance via FastAPI backend.
 * Compatible with the existing chat interface but uses FastAPI instead of Genkit.
 *
 * @param data - The query request containing user question and chat history
 * @returns A promise that resolves to the AI assistant's answer
 * @throws {Error} If the network request fails or the backend returns an error
 */
export const answerQueryWithFastAPI = async (
    data: QueryRequest,
): Promise<string> => {
    const { query, history } = data;
    const endpoint = `${FASTAPI_BASE_URL}/api/answer-query`;
    const controller = createTimeoutController(REQUEST_TIMEOUT);

    console.log(`Processing query with FastAPI: ${endpoint}`);

    try {
        const response = await fetch(endpoint, {
            method: "POST",
            headers: COMMON_HEADERS,
            body: JSON.stringify({
                query,
                history,
            }),
            signal: controller.signal,
        });

        if (!response.ok) {
            const errorData = await response
                .json()
                .catch(() => ({ detail: "Unknown error" }));
            throw new Error(
                `Backend error: ${response.status} - ${errorData.detail}`,
            );
        }

        const result = await response.json();
        return result.answer;
    } catch (error) {
        return handleAPIError(error, "process query with FastAPI");
    }
};

// --- Action to Analyze Contact Inquiry ---

/**
 * Analyzes and categorizes contact form submissions via FastAPI backend.
 * Provides sentiment analysis, urgency assessment, and suggested responses.
 *
 * @param data - The contact inquiry containing name, email, and message
 * @returns A promise that resolves to the analysis results
 * @throws {Error} If the network request fails or the backend returns an error
 */
export const analyzeContactWithFastAPI = async (
    data: ContactInquiryRequest,
): Promise<ContactAnalysisResponse["analysis"]> => {
    const { name, email, message } = data;
    const endpoint = `${FASTAPI_BASE_URL}/api/handle-contact-inquiry`;
    const controller = createTimeoutController(REQUEST_TIMEOUT);

    console.log(`Analyzing contact inquiry with FastAPI: ${endpoint}`);

    try {
        const response = await fetch(endpoint, {
            method: "POST",
            headers: COMMON_HEADERS,
            body: JSON.stringify({
                name,
                email,
                message,
            }),
            signal: controller.signal,
        });

        if (!response.ok) {
            const errorData = await response
                .json()
                .catch(() => ({ detail: "Unknown error" }));
            throw new Error(
                `Backend error: ${response.status} - ${errorData.detail}`,
            );
        }

        const result = await response.json();
        return result.analysis;
    } catch (error) {
        return handleAPIError(error, "analyze contact inquiry with FastAPI");
    }
};

// --- Health Check Action ---

/**
 * Checks if the FastAPI backend is available and healthy.
 * Useful for graceful fallback to Genkit implementation.
 *
 * @returns A promise that resolves to true if backend is healthy, false otherwise
 */
export const checkFastAPIHealth = async (): Promise<boolean> => {
    try {
        const controller = createTimeoutController(5000); // 5 second timeout for health check
        const response = await fetch(`${FASTAPI_BASE_URL}/health`, {
            method: "GET",
            headers: {
                Accept: "application/json",
            },
            signal: controller.signal,
        });

        return response.ok;
    } catch (error) {
        console.warn("FastAPI health check failed:", error);
        return false;
    }
};

// --- Migration Utilities ---

/**
 * Utility function to check FastAPI availability and provide graceful fallback
 * Can be used to conditionally use FastAPI or fall back to Genkit implementation
 *
 * @example
 * ```typescript
 * const isFastAPIAvailable = await checkFastAPIHealth();
 * if (isFastAPIAvailable) {
 *   result = await getItineraryFromFastAPI(data);
 * } else {
 *   result = await handleItineraryRequest(data);
 * }
 * ```
 */
export const withFastAPIFallback = async <T>(
    fastAPIOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
): Promise<T> => {
    const isHealthy = await checkFastAPIHealth();

    if (isHealthy) {
        try {
            return await fastAPIOperation();
        } catch (error) {
            console.warn(
                "FastAPI operation failed, falling back to Genkit:",
                error,
            );
            return await fallbackOperation();
        }
    } else {
        console.info("FastAPI not available, using Genkit fallback");
        return await fallbackOperation();
    }
};

// --- Type Exports ---

/**
 * Export types for use in other parts of the application
 */
export type {
    ItineraryRequest,
    FastAPIChatMessage,
    QueryRequest,
    ContactInquiryRequest,
    ContactAnalysisResponse,
};
